# Shadboard - Next.js 15 & Shadcn/UI Admin Dashboard Template

**Shadboard** is an open-source admin dashboard template built with Next.js 15 and Shadcn/ui components for scalable, user-friendly web apps. It's free for the community to use, learn from, and contribute to.

## Core Features

- **React 19:** Built using React 19 for enhanced performance, concurrency, and an exceptional developer experience.
- **Next.js 15:** Powered by Next.js 15, ensuring server-side rendering, SEO optimization, and app router support for smooth navigation.
- **Tailwind CSS:** Styled with Tailwind CSS, offering a utility-first approach for fast and responsive UI design that’s easy to customize.
- **Radix UI:** Utilizes Radix UI for high-quality, accessible, and unstyled components that ensure seamless interaction.
- **Internationalization (I18n):** Built-in support for multiple languages, making it easy to expand your app globally and serve diverse user bases.
- **Authentication:** Integrated user authentication and session management for secure user logins and data protection.
- **Customizer:** A tool for dynamically changing the style and colors of the dashboard, perfect for previewing and selecting your preferred theme.
- **Content Rich:** Includes pre-built apps and pages, along with reusable components, to speed up your development process.
- **Accessible:** Designed with accessibility in mind, ensuring usability for all users, including those with disabilities.
- **Pre-Made Layouts:** Offers ready-to-use layouts for dashboards, profiles, and other essential pages, enabling quick setups.
- **Responsive:** Fully responsive design, adapting seamlessly to different screen sizes for an optimal user experience across all devices.
- **Learning Resource:** Explore advanced web development approaches to build scalable, maintainable applications through included learning resources.
- **Well-Documented:** Comprehensive documentation for easy integration and customization, ensuring a smooth setup process for developers of all skill levels.

## Tools & Technologies

- **React 19** 
- **Next.js 15**
- **Radix UI + shadcn/ui**
- **Tailwind CSS 4** 
- **NextAuth.js**
- **Zod**
- **React Hook Form** 
- **Lucide** 
- **React Icons**
- **Recharts** 
- **TanStack Table** 
- **Embla Carousel** 
- **FullCalendar**

> For more details on the sources and credits used in Shadboard, visit the [sources & credits page](https://shadboard.vercel.app/docs/miscellaneous/sources-and-credits).

## Apps & Pages

- **Email**
- **Chat**
- **Calendar**
- **Kanban**
- **Pricing**
- **Payment**
- **General Settings**
- **Security Settings**
- **Plan & Billing Settings**
- **Notifications Settings**
- **Coming Soon**
- **Not Found 404**
- **Unauthorized 401**
- **Maintenance**
- **Forgot Password**
- **New Password**
- **Verify Email**
- **Register**
- **Sign In**

## Theme Customization

**Shadboard** offers easy theme customization, allowing you to choose from pre-defined colors, border radii, and modes to match your brand’s identity effortlessly. Whether you're building a light or dark theme, Shadboard provides the flexibility to quickly adjust the look and feel of your dashboard. Tailor the dashboard's appearance to your project's unique needs with just a few clicks!

## Documentation

Get started with detailed instructions and guides to help you integrate and customize **Shadboard** into your projects. Visit the full documentation here: [Docs](https://shadboard.vercel.app/docs)

## Contributing

We welcome contributions from the community! If you’d like to report bugs, suggest features, or contribute code, please check out our [contributing guidelines](https://github.com/Qualiora/shadboard/blob/main/CONTRIBUTING.md) for everything you need to get started.

## Live Demo

See Shadboard in action by visiting the [live demo](https://shadboard.vercel.app/) now!

## Get Started

Get your hands on **Shadboard** today and start building scalable, user-friendly applications with ease!
