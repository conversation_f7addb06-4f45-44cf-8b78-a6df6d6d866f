{"name": "shadboard-nextjs-starter-kit", "version": "1.0.0", "license": "MIT", "private": true, "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/LaythAlqadhi"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --ignore-path .gitignore --write ."}, "engines": {"node": ">=22", "pnpm": ">=10"}, "packageManager": "pnpm@10.8.1", "dependencies": {"@eslint/eslintrc": "3.2.0", "@radix-ui/react-alert-dialog": "1.1.1", "@radix-ui/react-avatar": "1.1.0", "@radix-ui/react-collapsible": "1.1.0", "@radix-ui/react-dialog": "1.1.3", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-dropdown-menu": "2.1.1", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-menubar": "1.1.1", "@radix-ui/react-scroll-area": "1.1.0", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-toast": "1.2.1", "@radix-ui/react-tooltip": "1.1.5", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "date-fns": "3.6.0", "embla-carousel-autoplay": "8.5.1", "embla-carousel-react": "8.5.1", "emoji-picker-react": "4.12.2", "input-otp": "1.4.2", "lucide-react": "0.446.0", "next": "15.2.4", "react": "19.1.0", "react-day-picker": "9.6.4", "react-dom": "19.1.0", "react-icons": "5.5.0", "react-use": "17.5.1", "sonner": "2.0.2", "tailwind-merge": "2.5.2", "vaul": "1.1.2", "zod": "3.23.8"}, "devDependencies": {"@eslint/compat": "1.2.7", "@ianvs/prettier-plugin-sort-imports": "4.4.1", "@tailwindcss/postcss": "4.0.17", "@tailwindcss/typography": "0.5.15", "@types/eslint__eslintrc": "2.1.2", "@types/node": "20", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "eslint": "9.18.0", "eslint-config-next": "15.2.4", "eslint-config-prettier": "10.1.1", "eslint-plugin-prettier": "5.2.3", "postcss": "8", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.11", "tailwindcss": "4.1.3", "tw-animate-css": "1.2.5", "typescript": "5"}, "overrides": {"@types/react": "19.0.12", "@types/react-dom": "19.0.4"}}