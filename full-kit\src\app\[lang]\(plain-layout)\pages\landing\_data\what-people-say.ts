import type { WhatPeopleSayType } from "../types"

export const whatPeopleSayData: WhatPeopleSayType[] = [
  {
    name: "<PERSON>",
    role: "Founder",
    company: "NovaPulse",
    quote:
      "I’m not even a designer and I launched a polished admin interface in a weekend. It’s that intuitive.",
    avatar: "/images/avatars/male-01.svg",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "<PERSON>O",
    company: "Acme Inc.",
    quote:
      "This dashboard template saved us weeks of development. It's clean, flexible, and packed with all the features we needed.",
    avatar: "/images/avatars/female-01.svg",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Lead Frontend Engineer",
    company: "ZypherWorks",
    quote:
      "Radix, Tailwind, and Next.js all in one place? Yes, please. Our whole team was productive within hours.",
    avatar: "/images/avatars/female-02.svg",
    rating: 4,
  },
  {
    name: "<PERSON>",
    role: "Head of Product",
    company: "NimbusCorp",
    quote:
      "From the design system to the documentation, everything is top-notch. Easily the best dashboard template I've used.",
    avatar: "/images/avatars/male-02.svg",
    rating: 5,
  },
  {
    name: "Priya Desai",
    role: "Software Architect",
    company: "QuantaEdge",
    quote:
      "Impressive attention to accessibility and performance. This isn’t just pretty—it’s production-ready.",
    avatar: "/images/avatars/female-03.svg",
    rating: 4,
  },
]
