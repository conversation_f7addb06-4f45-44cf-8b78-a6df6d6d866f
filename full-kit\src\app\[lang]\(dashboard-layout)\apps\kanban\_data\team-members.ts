import type { UserType } from "../types"

export const teamMembersData: UserType[] = [
  {
    id: "member-0",
    username: "john.doe",
    name: "<PERSON>",
    avatar: "/images/avatars/male-01.svg",
  },
  {
    id: "member-1",
    username: "emily.smith",
    name: "<PERSON>",
    avatar: "/images/avatars/female-01.svg",
  },
  {
    id: "member-2",
    username: "michael.brown",
    name: "<PERSON>",
    avatar: "/images/avatars/male-02.svg",
  },
  {
    id: "member-3",
    username: "sarah.johnson",
    name: "<PERSON>",
    avatar: "/images/avatars/female-02.svg",
  },
  {
    id: "member-4",
    username: "olivia.mart<PERSON><PERSON>",
    name: "<PERSON>",
    avatar: "/images/avatars/female-03.svg",
  },
]
