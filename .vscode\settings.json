{"editor.formatOnSave": true, "editor.formatOnPaste": false, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "html", "json"], "eslint.format.enable": true, "prettier.requireConfig": true, "files.insertFinalNewline": true, "files.eol": "\n", "files.associations": {"*.css": "tailwindcss"}}