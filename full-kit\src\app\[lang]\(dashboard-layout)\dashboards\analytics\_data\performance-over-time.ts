import type { PerformanceOverTimeType } from "../types"

export const performanceOverTimeData: PerformanceOverTimeType = {
  summary: {
    totalVisitors: 24500,
    totalConversions: 860,
  },
  performance: [
    { month: "January", visitors: 1300, conversions: 30 },
    { month: "February", visitors: 2200, conversions: 60 },
    { month: "March", visitors: 3200, conversions: 80 },
    { month: "April", visitors: 2100, conversions: 40 },
    { month: "May", visitors: 4000, conversions: 120 },
    { month: "June", visitors: 2300, conversions: 70 },
    { month: "July", visitors: 4500, conversions: 150 },
    { month: "August", visitors: 3700, conversions: 110 },
    { month: "September", visitors: 5000, conversions: 180 },
    { month: "October", visitors: 3000, conversions: 70 },
    { month: "November", visitors: 5500, conversions: 200 },
    { month: "December", visitors: 6000, conversions: 250 },
  ],
}
