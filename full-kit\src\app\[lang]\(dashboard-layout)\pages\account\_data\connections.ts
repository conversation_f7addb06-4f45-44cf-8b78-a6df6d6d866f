import type { UserProfileConnectionType } from "../types"

export const connectionsData: UserProfileConnectionType[] = [
  {
    name: "<PERSON>",
    avatar: "/images/avatars/male-01.svg",
    connections: 1240,
  },
  {
    name: "<PERSON>",
    avatar: "/images/avatars/female-01.svg",
    connections: 980,
  },
  {
    name: "<PERSON>",
    avatar: "/images/avatars/male-02.svg",
    connections: 2150,
  },
  {
    name: "<PERSON>",
    avatar: "/images/avatars/female-03.svg",
    connections: 1875,
  },
  {
    name: "<PERSON>",
    avatar: "/images/avatars/female-02.svg",
    connections: 3420,
  },
]
