@import "tailwindcss";
@import "tw-animate-css";
@plugin "@tailwindcss/typography";
@import "./themes.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --font-lato:
    var(--font-lato), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-cairo:
    var(--font-cairo), ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-success: hsl(var(--success));
  --color-success-foreground: hsl(var(--success-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  --color-sidebar: hsl(var(--sidebar-background));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-ring: hsl(var(--sidebar-ring));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-collapsible-down: collapsible-down 0.2s ease-out;
  --animate-collapsible-up: collapsible-up 0.2s ease-out;
  --animate-collapsible-right: collapsible-right 0.2s ease-out;
  --animate-collapsible-left: collapsible-left 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes collapsible-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }
  @keyframes collapsible-up {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes collapsible-right {
    from {
      width: 0;
    }
    to {
      width: var(--radix-collapsible-content-width);
    }
  }
  @keyframes collapsible-left {
    from {
      width: var(--radix-collapsible-content-width);
    }
    to {
      width: 0;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 1rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-border, currentColor);
  }
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --primary: 240 5.9% 10%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 4.8% 95.9%;
  --secondary-foreground: 240 5.9% 10%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --success: 142.1 76.2% 36.3%;
  --success-foreground: 0 0% 98%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 240 10% 3.9%;
  --radius: 0.5rem;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
  --sidebar-background: var(--background);
  --sidebar-foreground: var(--foreground);
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: var(--accent);
  --sidebar-accent-foreground: var(--accent-foreground);
  --sidebar-border: var(--border);
  --sidebar-ring: var(--ring);

  /* Calendar vars */
  --fc-small-font-size: 0.875em;
  --fc-page-bg-color: hsl(var(--border));
  --fc-neutral-bg-color: hsl(var(--border));
  --fc-neutral-text-color: hsl(var(--accent-foreground));
  --fc-border-color: hsl(var(--border));
  --fc-button-text-color: hsl(var(--background));
  --fc-button-bg-color: hsl(var(--primary));
  --fc-button-border-color: hsl(var(--primary));
  --fc-button-hover-bg-color: hsl(var(--primary) / 0.8);
  --fc-button-hover-border-color: hsl(var(--primary));
  --fc-button-active-bg-color: hsl(var(--primary) / 0.8);
  --fc-button-active-border-color: hsl(var(--primary) / 0);
  --fc-event-bg-color: hsl(var(--primary));
  --fc-event-border-color: hsl(var(--primary));
  --fc-event-text-color: hsl(var(--background));
  --fc-event-selected-overlay-color: hsl(var(--muted));
  --fc-more-link-bg-color: hsl(var(--muted));
  --fc-more-link-text-color: inherit;
  --fc-event-resizer-thickness: 8px;
  --fc-event-resizer-dot-total-width: 8px;
  --fc-event-resizer-dot-border-width: var(--radius);
  --fc-non-business-color: rgba(215, 215, 215, 0.3);
  --fc-bg-event-color: hsl(var(--success));
  --fc-bg-event-opacity: 0.3;
  --fc-highlight-color: rgba(188, 232, 241, 0.3);
  --fc-today-bg-color: hsl(var(--primary) / 0.15);
  --fc-now-indicator-color: hsl(var(--destructive));
}
.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 240 5.9% 10%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 84.2% 35.2%;
  --destructive-foreground: 0 0% 98%;
  --success: 142.4 71.8% 29.2%;
  --success-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

aside.EmojiPickerReact {
  /* Emoji Picker vars */
  --epr-emoji-size: 2rem;
  --epr-emoji-padding: 0.25rem;
  --epr-emoji-gap: 0.5rem;
  --epr-hover-bg-color: hsl(var(--accent));
  --epr-focus-bg-color: hsl(var(--accent));
  --epr-bg-color: hsl(var(--background));
  --epr-reactions-bg-color: hsl(var(--background));
  --epr-highlight-color: hsl(var(--primary));
  --epr-category-icon-active-color: hsl(var(--primary));
  --epr-category-label-bg-color: hsl(var(--background));
  --epr-text-color: hsl(var(--foreground));
  --epr-horizontal-padding: 0.75rem;
  --epr-search-input-bg-color: hsl(var(--muted));
  --epr-search-input-border-radius: var(--radius);
  --epr-search-input-padding: 0 40px;
  --epr-preview-text-size: 0.875rem;
  --epr-header-padding: var(--epr-horizontal-padding)
    var(--epr-horizontal-padding);
  --epr-picker-border-radius: var(--radius);
  --epr-picker-border-color: hsl(var(--border));
  --epr-skin-tone-outer-border-color: hsl(var(--border));
}
.EmojiPickerReact .epr-btn {
  border-radius: var(--radius);
}
.EmojiPickerReact .epr-btn:focus::before {
  border-radius: var(--radius);
  border-width: 1px;
}
