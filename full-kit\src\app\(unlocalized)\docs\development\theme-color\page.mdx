# Theme Color

This guide details how to customize the theme color of your dashboard by modifying the `globals.css` file.

---

## 1. Overview

Theme customization is essential for aligning your dashboard's aesthetics with your brand or personal preferences. By editing the `globals.css` file, you gain granular control over the color scheme, ensuring a cohesive user experience.

---

## 2. Customization via `globals.css`

### 2.1. **Navigate to the `globals.css` file**

The `globals.css` file is located at `src/app/globals.css`. This file defines all theme colors. Below is the default structure for theme colors:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }

  /* Dark Mode */
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}
```

### 2.2. **Modify Color Variables:**

- Update the HSL values of the CSS variables within the `:root` (light mode) and `.dark` (dark mode) selectors.
- **Important:** Maintain the HSL format (Hue, Saturation, Lightness) for color consistency and compatibility.
- Utilize tools like:
  - [Shadcn/ui color picker](https://ui.shadcn.com/colors) for HSL color selection.
  - [HSL to HEX by Easy Retro](https://easyretro.io/tools/hsl-to-hex/) for conversions.
- Example: Change primary color to a blue tone.

```css
--primary: 200 80% 50%;
```

### 2.3. **Remove Unnecessary Files**

Once you've set your theme colors, delete the following files:

- `src/configs/base-colors.ts`
- `src/providers/theme-provider.tsx`

### 2.4. **Update Types**

- Remove the `BaseColorType` definition in `src/types.ts`.
- Remove any `baseColor` imports from other files.

### 2.5. **Remove `ThemeProvider`**

- In `src/providers/index.tsx`, remove the `ThemeProvider` and its import.

### 2.6. **Update `settings-context.tsx`**

In the `src/contexts/settings-context.tsx` file, remove the `theme` property from the `defaultSettings` object.

### 2.7. **Update `src/types.ts`**

Ensure that the `SettingsType` type in `src/types.ts` no longer includes the `theme` property.

### 2.8. **Hard Refresh**

After completing the changes, perform a **hard refresh (Ctrl + F5)** on your website to apply the updated theme.

---

## 3. Additional Resources

For more details on theming in Shadcn/ui, refer to the official documentation:

[Shadcn/ui Theming Documentation](https://ui.shadcn.com/docs/theming)
