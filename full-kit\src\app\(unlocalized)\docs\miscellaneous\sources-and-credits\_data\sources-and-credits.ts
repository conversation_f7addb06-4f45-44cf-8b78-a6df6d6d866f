export const sourcesAndCreditsData = [
  {
    name: "@auth/prisma-adapter",
    url: "https://github.com/nextauthjs/next-auth",
    license: "ISC",
  },
  {
    name: "@commitlint/cli",
    url: "https://github.com/conventional-changelog/commitlint",
    license: "MIT",
  },
  {
    name: "@commitlint/config-conventional",
    url: "https://github.com/conventional-changelog/commitlint",
    license: "MIT",
  },
  {
    name: "@eslint/compat",
    url: "https://github.com/eslint/rewrite",
    license: "Apache-2.0",
  },
  {
    name: "@formatjs/intl-localematcher",
    url: "https://github.com/formatjs/formatjs",
    license: "MIT",
  },
  {
    name: "@fullcalendar/core",
    url: "https://github.com/fullcalendar/fullcalendar",
    license: "MIT",
  },
  {
    name: "@hello-pangea/dnd",
    url: "https://github.com/hello-pangea/dnd",
    license: "MIT",
  },
  {
    name: "@hookform/resolvers",
    url: "https://github.com/react-hook-form/resolvers",
    license: "MIT",
  },
  {
    name: "@ianvs/prettier-plugin-sort-imports",
    url: "https://github.com/ianvs/prettier-plugin-sort-imports",
    license: "Apache-2.0",
  },
  {
    name: "@mdx-js/loader",
    url: "https://github.com/mdx-js/mdx",
    license: "MIT",
  },
  {
    name: "@mdx-js/react",
    url: "https://github.com/mdx-js/mdx",
    license: "MIT",
  },
  {
    name: "@prisma/client",
    url: "https://github.com/prisma/prisma",
    license: "Apache-2.0",
  },
  {
    name: "@radix-ui/react-accessible-icon",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-accordion",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-alert-dialog",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-aspect-ratio",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-avatar",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-checkbox",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-collapsible",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-context-menu",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-dialog",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-direction",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-dropdown-menu",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-hover-card",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-label",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-menubar",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-navigation-menu",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-popover",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-progress",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-radio-group",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-scroll-area",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-select",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-separator",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-slider",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-slot",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-switch",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-tabs",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-toast",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-toggle",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-toggle-group",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@radix-ui/react-tooltip",
    url: "https://github.com/radix-ui/primitives",
    license: "MIT",
  },
  {
    name: "@semantic-release/github",
    url: "https://github.com/semantic-release/github",
    license: "MIT",
  },
  {
    name: "@tailwindcss/typography",
    url: "https://github.com/tailwindlabs/tailwindcss-typography",
    license: "MIT",
  },
  {
    name: "@tanstack/react-table",
    url: "https://github.com/TanStack/react-table",
    license: "MIT",
  },
  {
    name: "@tiptap/extension-color",
    url: "https://github.com/ueberdosis/tiptap",
    license: "MIT",
  },
  {
    name: "@tiptap/extension-image",
    url: "https://github.com/ueberdosis/tiptap",
    license: "MIT",
  },
  {
    name: "@tiptap/extension-link",
    url: "https://github.com/ueberdosis/tiptap",
    license: "MIT",
  },
  {
    name: "@tiptap/extension-placeholder",
    url: "https://github.com/ueberdosis/tiptap",
    license: "MIT",
  },
  {
    name: "@tiptap/extension-text-align",
    url: "https://github.com/ueberdosis/tiptap",
    license: "MIT",
  },
  {
    name: "@tiptap/extension-typography",
    url: "https://github.com/ueberdosis/tiptap",
    license: "MIT",
  },
  {
    name: "@tiptap/extension-underline",
    url: "https://github.com/ueberdosis/tiptap",
    license: "MIT",
  },
  {
    name: "@tiptap/react",
    url: "https://github.com/ueberdosis/tiptap",
    license: "MIT",
  },
  {
    name: "@tiptap/starter-kit",
    url: "https://github.com/ueberdosis/tiptap",
    license: "MIT",
  },
  {
    name: "@types/eslint__eslintrc",
    url: "https://github.com/eslint/eslintrc",
    license: "MIT",
  },
  {
    name: "@types/mdx",
    url: "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mdx",
    license: "MIT",
  },
  {
    name: "@types/negotiator",
    url: "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/negotiator",
    license: "MIT",
  },
  {
    name: "@types/node",
    url: "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node",
    license: "MIT",
  },
  {
    name: "@types/react",
    url: "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react",
    license: "MIT",
  },
  {
    name: "@types/react-dom",
    url: "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-dom",
    license: "MIT",
  },
  {
    name: "class-variance-authority",
    url: "https://github.com/nextui-org/class-variance-authority",
    license: "MIT",
  },
  {
    name: "clsx",
    url: "https://github.com/lukeed/clsx",
    license: "MIT",
  },
  {
    name: "cmdk",
    url: "https://github.com/pacocoursey/cmdk",
    license: "MIT",
  },
  {
    name: "date-fns",
    url: "https://github.com/date-fns/date-fns",
    license: "MIT",
  },
  {
    name: "embla-carousel-autoplay",
    url: "https://github.com/davidcetinkaya/embla-carousel",
    license: "MIT",
  },
  {
    name: "embla-carousel-react",
    url: "https://github.com/davidcetinkaya/embla-carousel",
    license: "MIT",
  },
  {
    name: "emoji-picker-react",
    url: "https://github.com/ealush/emoji-picker-react",
    license: "MIT",
  },
  {
    name: "eslint",
    url: "https://github.com/eslint/eslint",
    license: "MIT",
  },
  {
    name: "eslint-config-next",
    url: "https://github.com/nextjs/eslint-config-next",
    license: "MIT",
  },
  {
    name: "eslint-config-prettier",
    url: "https://github.com/prettier/eslint-config-prettier",
    license: "MIT",
  },
  {
    name: "eslint-plugin-prettier",
    url: "https://github.com/prettier/eslint-plugin-prettier",
    license: "MIT",
  },
  {
    name: "input-otp",
    url: "https://github.com/adiwajshing/input-otp",
    license: "MIT",
  },
  {
    name: "Logoipsum",
    url: "https://logoipsum.com",
    license: "Custom Fair Use License",
  },
  {
    name: "lucide-react",
    url: "https://github.com/lucide-icons/lucide",
    license: "MIT",
  },
  {
    name: "negotiator",
    url: "https://github.com/jedwards/negotiator",
    license: "MIT",
  },
  {
    name: "next",
    url: "https://github.com/vercel/next.js",
    license: "MIT",
  },
  {
    name: "next-auth",
    url: "https://github.com/nextauthjs/next-auth",
    license: "ISC",
  },
  {
    name: "Open Peeps",
    url: "https://www.openpeeps.com/",
    license: "CC0",
  },
  {
    name: "postcss",
    url: "https://github.com/postcss/postcss",
    license: "MIT",
  },
  {
    name: "prettier",
    url: "https://github.com/prettier/prettier",
    license: "MIT",
  },
  {
    name: "prettier-plugin-tailwindcss",
    url: "https://github.com/tailwindlabs/prettier-plugin-tailwindcss",
    license: "MIT",
  },
  {
    name: "react",
    url: "https://github.com/facebook/react",
    license: "MIT",
  },
  {
    name: "react-day-picker",
    url: "https://github.com/wojtekmaj/react-day-picker",
    license: "MIT",
  },
  {
    name: "react-dom",
    url: "https://github.com/facebook/react",
    license: "MIT",
  },
  {
    name: "react-dropzone",
    url: "https://github.com/react-dropzone/react-dropzone",
    license: "MIT",
  },
  {
    name: "react-hook-form",
    url: "https://github.com/react-hook-form/react-hook-form",
    license: "MIT",
  },
  {
    name: "react-icons",
    url: "https://github.com/react-icons/react-icons",
    license: "MIT",
  },
  {
    name: "react-phone-number-input",
    url: "https://github.com/apatryk/react-phone-number-input",
    license: "MIT",
  },
  {
    name: "react-resizable-panels",
    url: "https://github.com/6eDesign/react-resizable-panels",
    license: "MIT",
  },
  {
    name: "react-use",
    url: "https://github.com/streamich/react-use",
    license: "Unlicense",
  },
  {
    name: "recharts",
    url: "https://github.com/recharts/recharts",
    license: "MIT",
  },
  {
    name: "shadcn/ui",
    url: "https://github.com/shadcn/ui",
    license: "MIT",
  },
  {
    name: "shiki",
    url: "https://github.com/shikijs/shiki",
    license: "MIT",
  },
  {
    name: "semantic-release",
    url: "https://github.com/semantic-release/semantic-release",
    license: "MIT",
  },
  {
    name: "sonner",
    url: "https://github.com/sonnerjs/sonner",
    license: "MIT",
  },
  {
    name: "tailwind-merge",
    url: "https://github.com/daisyui/tailwind-merge",
    license: "MIT",
  },
  {
    name: "tailwindcss",
    url: "https://github.com/tailwindlabs/tailwindcss",
    license: "MIT",
  },
  {
    name: "tailwindcss-animate",
    url: "https://github.com/justinwark/tailwindcss-animate",
    license: "MIT",
  },
  {
    name: "tw-animate-css",
    url: "https://github.com/Wombosvideo/tw-animate-css",
    license: "MIT",
  },
  {
    name: "typescript",
    url: "https://github.com/Microsoft/TypeScript",
    license: "Apache-2.0",
  },
  {
    name: "vaul",
    url: "https://github.com/vaul-app/vaul",
    license: "MIT",
  },
  {
    name: "zod",
    url: "https://github.com/colinhacks/zod",
    license: "MIT",
  },
]
