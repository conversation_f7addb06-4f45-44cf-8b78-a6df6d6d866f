import type { FaqType } from "../types"

export const faqsData: FaqType[] = [
  {
    question: "Is this dashboard template really free?",
    answer:
      "Yes! It's 100% free and open-source. You can use it for personal, commercial, or educational projects without paying a cent.",
  },
  {
    question: "Can I use it in commercial projects?",
    answer:
      "Absolutely. It's MIT-licensed, so you can use, modify, and distribute it—even in commercial applications.",
  },
  {
    question: "How do I get started?",
    answer:
      "Just clone the repository, install the dependencies, and run the development server. Full setup instructions are included in the documentation.",
  },
  {
    question: "Is it mobile responsive?",
    answer:
      "Yes, the dashboard is fully responsive and optimized for all screen sizes, from mobile to desktop.",
  },
  {
    question: "Can I customize the design?",
    answer:
      "Definitely. It uses Tailwind CSS and shadcn/ui components, making it easy to adapt the design to your brand.",
  },
  {
    question: "Does it support authentication?",
    answer:
      "Yes. The template includes integrated authentication and session management to help you hit the ground running.",
  },
  {
    question: "Where can I find the source code?",
    answer:
      "You can find the full source code on GitHub. Contributions are welcome!",
  },
]
