# Introduction

Welcome to the documentation for **Shadboard**, an open-source admin dashboard template built with **Next.js** and **Shadcn/ui** components for scalable, user-friendly web apps. It's free for the community to use, learn from, and contribute to.

Shadboard leverages the powerful **Shadcn/ui** component library, **Tailwind CSS 4**, and the latest technologies such as **React 19**, **Next.js 15**, and **TypeScript** to ensure a highly efficient, flexible, and user-friendly experience. For more details on the Shadcn/ui component library, visit [Shadcn/ui](https://ui.shadcn.com/).

## Key Features of Shadboard

- **Light/Dark Mode**: Seamlessly switch between light and dark themes for an enhanced user experience.
- **Responsive Design**: Fully responsive, ensuring the dashboard adapts beautifully to all screen sizes and devices.
- **Horizontal/Vertical Layouts**: Choose between multiple layout options to suit your design needs.
- **LTR & RTL Support**: Built-in support for both Left-to-Right (LTR) and Right-to-Left (RTL) text directions.
- **Internationalization (I18n) Support**: Easily implement multiple languages and make your application globally accessible.
- **Starter Kit Available**: A ready-to-use starter kit to help you get started quickly with a base template.
- **Highly Customizable**: Easily customize the look and feel of your dashboard with configurable options.
- **Built with React & Next.js 15**: Powered by **Next.js 15** for enhanced performance, server-side rendering, and dynamic routing.
- **Tailwind CSS**: Leverages **Tailwind CSS** for a utility-first approach, ensuring easy styling and layout management.
- **TypeScript/JavaScript Support**: Supports both **TypeScript** and **JavaScript**, catering to a wide range of developer preferences.
- **React Form Hook**: Simplify form management with **React Hook Form** for easy validation and state management.
- **NextAuth.js Integration**: Simplifies authentication with **NextAuth.js** for secure and easy login processes.
- **Shadcn/ui Components**: Built with the **Shadcn/ui** library for a modern, clean, and highly customizable component system.
