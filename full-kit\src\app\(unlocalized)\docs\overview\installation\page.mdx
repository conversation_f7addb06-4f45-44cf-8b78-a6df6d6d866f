# Installation

This guide provides step-by-step instructions to install and set up your project using either the **Starter Kit** or the **Full Kit** version.

---

## 1. Prerequisites

Before starting the installation, ensure that you have the following installed on your system:

1. **Node.js** (version 18.18 or later)
   You can download Node.js from: [https://nodejs.org/](https://nodejs.org/).

2. **pnpm** (Preferred Node Package Manager)
   Install pnpm globally using the following command:

   ```bash
   npm install -g pnpm
   ```

   Confirm installation with:

   ```bash
   pnpm --version
   ```

3. **Supported Operating Systems**:
   The project supports the following operating systems:

   - **macOS**
   - **Windows** (including WSL)
   - **Linux**

4. **Code Editor**
   A code editor is needed to modify your project files. We recommend [VSCode](https://code.visualstudio.com/), but you can use your preferred editor.

---

## 2. Download the Project

Follow these steps to download and choose the version that suits your needs:

1. **Download** the zipped project file and save it to your desired directory.
2. **Extract** the zip file:

   - Inside, you'll find two folders: `typescript-version` and `javascript-version`.
   - Choose a version based on your setup preference:

     - **Full Kit**: Includes all features, apps, and pages for a complete project.
     - **Starter Kit**: A minimal setup with core features for easy customization.

3. Place your selected version in your workspace directory and open it in your code editor.

---

## 3. Install Dependencies

After extracting the project and selecting your preferred version, install the required dependencies using pnpm:

```bash
pnpm install
```

---

## 4. Launch Your Project

To start the development server, run the following command:

```bash
pnpm run dev
```

The project will be available at **[http://localhost:3000](http://localhost:3000)** by default.

Enjoy building your application! 🎉
