{"name": "shadboard-nextjs-full-kit", "version": "1.0.0", "license": "MIT", "private": true, "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/LaythAlqadhi"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --ignore-path .gitignore --write .", "migrate": "pnpm exec prisma migrate dev", "postinstall": "prisma generate"}, "engines": {"node": ">=22", "pnpm": ">=10"}, "packageManager": "pnpm@10.8.1", "dependencies": {"@auth/prisma-adapter": "2.6.0", "@eslint/eslintrc": "3.2.0", "@formatjs/intl-localematcher": "0.5.4", "@fullcalendar/core": "6.1.15", "@fullcalendar/daygrid": "6.1.15", "@fullcalendar/interaction": "6.1.15", "@fullcalendar/list": "6.1.15", "@fullcalendar/react": "6.1.15", "@fullcalendar/timegrid": "6.1.15", "@hello-pangea/dnd": "18.0.1", "@hookform/resolvers": "3.9.0", "@mdx-js/loader": "3.1.0", "@mdx-js/react": "3.1.0", "@next/mdx": "15.2.4", "@prisma/client": "5.20.0", "@radix-ui/react-accessible-icon": "1.1.0", "@radix-ui/react-accordion": "1.2.0", "@radix-ui/react-alert-dialog": "1.1.1", "@radix-ui/react-aspect-ratio": "1.1.0", "@radix-ui/react-avatar": "1.1.0", "@radix-ui/react-checkbox": "1.1.1", "@radix-ui/react-collapsible": "1.1.0", "@radix-ui/react-context-menu": "2.2.1", "@radix-ui/react-dialog": "1.1.3", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-dropdown-menu": "2.1.1", "@radix-ui/react-hover-card": "1.1.1", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-menubar": "1.1.1", "@radix-ui/react-navigation-menu": "1.2.0", "@radix-ui/react-popover": "1.1.1", "@radix-ui/react-progress": "1.1.0", "@radix-ui/react-radio-group": "1.2.0", "@radix-ui/react-scroll-area": "1.1.0", "@radix-ui/react-select": "2.1.1", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.0", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.0", "@radix-ui/react-tabs": "1.1.0", "@radix-ui/react-toast": "1.2.1", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-tooltip": "1.1.5", "@tanstack/react-table": "8.20.5", "@tiptap/extension-bubble-menu": "2.11.2", "@tiptap/extension-color": "2.9.1", "@tiptap/extension-image": "2.9.1", "@tiptap/extension-link": "2.9.1", "@tiptap/extension-placeholder": "2.9.1", "@tiptap/extension-text-align": "2.9.1", "@tiptap/extension-typography": "2.9.1", "@tiptap/extension-underline": "2.9.1", "@tiptap/react": "2.11.7", "@tiptap/starter-kit": "2.11.7", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "cmdk": "1.1.1", "date-fns": "3.6.0", "embla-carousel-autoplay": "8.5.1", "embla-carousel-react": "8.5.1", "emoji-picker-react": "4.12.2", "input-otp": "1.4.2", "lucide-react": "0.446.0", "negotiator": "0.6.3", "next": "15.2.4", "next-auth": "4.24.11", "react": "19.1.0", "react-day-picker": "9.6.4", "react-dom": "19.1.0", "react-dropzone": "14.2.10", "react-hook-form": "7.53.0", "react-icons": "5.5.0", "react-phone-number-input": "3.4.5", "react-resizable-panels": "2.1.7", "react-use": "17.5.1", "recharts": "2.15.1", "shiki": "3.2.1", "sonner": "2.0.2", "tailwind-merge": "2.5.2", "vaul": "1.1.2", "zod": "3.23.8"}, "devDependencies": {"@eslint/compat": "1.2.7", "@ianvs/prettier-plugin-sort-imports": "4.4.1", "@tailwindcss/postcss": "4.0.17", "@tailwindcss/typography": "0.5.15", "@types/eslint__eslintrc": "2.1.2", "@types/mdx": "2.0.13", "@types/negotiator": "0.6.3", "@types/node": "20", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "eslint": "9.18.0", "eslint-config-next": "15.2.4", "eslint-config-prettier": "10.1.1", "eslint-plugin-prettier": "5.2.3", "postcss": "8", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.11", "prisma": "5.20.0", "tailwindcss": "4.1.3", "tw-animate-css": "1.2.5", "typescript": "5"}, "overrides": {"@types/react": "19.0.12", "@types/react-dom": "19.0.4"}}