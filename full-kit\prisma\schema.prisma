datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id                   String          @id @default(uuid())
  name                 String
  username             String?         @unique
  email                String?         @unique
  emailVerifyToken     String?
  emailVerified        DateTime?
  password             String?
  passwordResetToken   String?
  passwordResetExpires DateTime?
  avatar               String?
  profileBackground    String?
  status               String          @default("ONLINE")
  preferences          UserPreference?
  accounts             Account[]
  sessions             Session[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([name, username, status, createdAt])
}

model UserPreference {
  id        String  @id @default(uuid())
  theme     String
  mode      String
  radius    String
  layout    String
  direction String
  user      User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String? @unique
}

model Account {
  id                String  @id @default(uuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(uuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
}
